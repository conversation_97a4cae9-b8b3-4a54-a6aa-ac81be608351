# 抖音巨量转化上传器

## 功能描述

这个Python脚本实现了抖音巨量转化结果的自动上传功能，包括：

1. 从数据库提取需要回传的callback_clickid
2. 调用抖音巨量上报接口进行转化回传
3. 更新数据库中的回传状态

## 文件结构

```
├── douyin_conversion_uploader.py  # 主程序文件
├── config.py                      # 配置文件
├── test_douyin_uploader.py        # 测试文件
└── README_douyin_uploader.md      # 说明文档
```

## 依赖要求

```bash
pip install psycopg2-binary requests
```

## 配置说明

### config.py 配置项

```python
# 数据库配置
DATABASE_CONFIG = {
    'dbname': 'your_database',
    'user': 'your_username',
    'password': 'your_password',
    'host': 'your_host',
    'port': 5432
}

# 抖音巨量API配置
DOUYIN_API_CONFIG = {
    'conversion_url': 'https://analytics.oceanengine.com/api/v2/conversion',
    'event_type': 'work_wechat_added',
    'timeout': 30,
    'max_retries': 1
}

# 日志配置
LOGGING_CONFIG = {
    'level': 'INFO',
    'format': '%(asctime)s - %(levelname)s - %(message)s',
    'filename': 'douyin_conversion.log'  # 可选
}
```

## 使用方法

### 1. 基本使用

```python
from douyin_conversion_uploader import DouyinConversionUploader

# 使用默认配置
uploader = DouyinConversionUploader()
uploader.process_conversions()
```

### 2. 自定义数据库配置

```python
custom_db_config = {
    'dbname': 'custom_db',
    'user': 'custom_user',
    'password': 'custom_pass',
    'host': 'custom_host',
    'port': 5432
}

uploader = DouyinConversionUploader(custom_db_config)
uploader.process_conversions()
```

### 3. 命令行运行

```bash
# 直接运行
python douyin_conversion_uploader.py

# 后台运行并记录日志
nohup python douyin_conversion_uploader.py > conversion.log 2>&1 &
```

## 数据库表结构

程序依赖的数据库表：`dm.dm_douyin_oceanengine_click_conversion`

关键字段：
- `callback_clickid`: 抖音巨量回传参数clickid
- `callback_status`: 数据回传状态 ('0': 待回传, '1': 成功, '2': 失败)
- `click_timestamp`: 点击时间
- `update_dtm`: 记录更新时间

## API接口说明

### 抖音巨量转化上报接口

- **接口地址**: https://analytics.oceanengine.com/api/v2/conversion
- **请求方式**: POST
- **请求体格式**:
```json
{
    "event_type": "work_wechat_added",
    "context": {
        "ad": {
            "callback": "clickid_value"
        }
    },
    "timestamp": 1604888786102
}
```

- **成功响应**:
```json
{
    "code": 0,
    "message": "成功"
}
```

## 处理逻辑

1. **提取待回传数据**: 查询昨天的待回传clickid（callback_status = '0'）
2. **调用上报接口**: 逐个发送转化数据到抖音巨量
3. **重试机制**: 失败时重试1次
4. **状态更新**: 
   - 成功: callback_status = '1'
   - 失败: callback_status = '2'

## 测试

### 运行单元测试

```bash
python test_douyin_uploader.py
```

### 运行手动测试（连接真实数据库和API）

```bash
python test_douyin_uploader.py manual
```

## 日志说明

程序会记录以下信息：
- 获取到的待回传clickid数量
- 每个clickid的上传结果
- 重试情况
- 最终处理统计（成功/失败数量）
- 异常错误信息

## 定时任务配置

可以使用crontab设置定时任务，建议每天运行一次：

```bash
# 每天凌晨2点执行
0 2 * * * cd /path/to/your/script && python douyin_conversion_uploader.py
```

## 注意事项

1. **数据安全**: 确保数据库连接配置的安全性
2. **API限制**: 注意抖音巨量API的调用频率限制
3. **网络环境**: 确保服务器能够访问抖音巨量API
4. **错误处理**: 程序包含完整的异常处理，但建议监控日志
5. **数据一致性**: 程序使用事务确保数据一致性

## 故障排查

### 常见问题

1. **数据库连接失败**
   - 检查数据库配置
   - 确认网络连通性
   - 验证用户权限

2. **API调用失败**
   - 检查网络连接
   - 验证API地址
   - 查看抖音巨量API文档

3. **没有待回传数据**
   - 检查SQL查询条件
   - 确认数据表中有相关数据
   - 验证时间范围设置

### 日志分析

查看日志文件了解详细的执行情况：
```bash
tail -f douyin_conversion.log
```
