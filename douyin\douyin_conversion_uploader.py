import json
import time
import requests
import psycopg2
from datetime import datetime, timedelta
import logging
from config import DATABASE_CONFIG, DOUYIN_API_CONFIG, LOGGING_CONFIG

class DouyinConversionUploader:
    def __init__(self, db_config=None):
        """
        初始化抖音巨量转化上传器
        :param db_config: 数据库连接配置，如果为None则使用默认配置
        """
        self.db_config = db_config or DATABASE_CONFIG
        self.api_url = DOUYIN_API_CONFIG['conversion_url']
        self.event_type = DOUYIN_API_CONFIG['event_type']
        self.timeout = DOUYIN_API_CONFIG['timeout']
        self.max_retries = DOUYIN_API_CONFIG['max_retries']

        # 配置日志
        log_config = {
            'level': getattr(logging, LOGGING_CONFIG['level']),
            'format': LOGGING_CONFIG['format']
        }
        if LOGGING_CONFIG.get('filename'):
            log_config['filename'] = LOGGING_CONFIG['filename']

        logging.basicConfig(**log_config)
        self.logger = logging.getLogger(__name__)
        
    def get_pending_clickids(self):
        """
        提取需要回传的callback_clickid
        :return: clickid列表
        """
        sql = """
        SELECT DISTINCT a.callback_clickid 
        FROM dm.dm_douyin_oceanengine_click_conversion a 
        WHERE a.callback_status = '0' 
          AND a.click_timestamp::date = current_date - 1
        """
        
        try:
            conn = psycopg2.connect(**self.db_config)
            cursor = conn.cursor()
            
            cursor.execute(sql)
            results = cursor.fetchall()
            
            clickids = [row[0] for row in results if row[0]]  # 过滤空值
            
            self.logger.info(f"获取到 {len(clickids)} 个待回传的clickid")
            return clickids
            
        except Exception as e:
            self.logger.error(f"获取待回传clickid失败: {e}")
            raise
        finally:
            if 'cursor' in locals():
                cursor.close()
            if 'conn' in locals():
                conn.close()
    
    def upload_conversion(self, clickid):
        """
        调用抖音巨量上报接口
        :param clickid: 回传参数clickid
        :return: (success, response_data)
        """
        # 构建请求体
        request_data = {
            "event_type": self.event_type,
            "context": {
                "ad": {
                    "callback": clickid
                }
            },
            "timestamp": int(time.time() * 1000)  # 当前时间戳（毫秒）
        }

        headers = {
            "Content-Type": "application/json"
        }

        max_retries = self.max_retries
        retry_count = 0
        
        while retry_count <= max_retries:
            try:
                if retry_count > 0:
                    self.logger.info(f"clickid {clickid} 第{retry_count}次重试")
                    time.sleep(1)  # 重试前等待1秒
                
                response = requests.post(
                    self.api_url,
                    data=json.dumps(request_data, ensure_ascii=False).encode('utf-8'),
                    headers=headers,
                    timeout=self.timeout
                )
                
                response_data = response.json()
                
                # 检查响应状态
                if response_data.get("code") == 0:
                    self.logger.info(f"clickid {clickid} 上报成功")
                    return True, response_data
                else:
                    self.logger.warning(f"clickid {clickid} 上报失败: {response_data}")
                    retry_count += 1
                    
            except requests.exceptions.RequestException as e:
                self.logger.error(f"clickid {clickid} 请求异常: {e}")
                retry_count += 1
            except json.JSONDecodeError as e:
                self.logger.error(f"clickid {clickid} 响应解析失败: {e}")
                retry_count += 1
            except Exception as e:
                self.logger.error(f"clickid {clickid} 未知错误: {e}")
                retry_count += 1
        
        # 所有重试都失败
        self.logger.error(f"clickid {clickid} 上报失败，已达到最大重试次数")
        return False, None
    
    def update_callback_status(self, clickid, status):
        """
        更新callback_status状态
        :param clickid: 回传参数clickid
        :param status: 状态 ('1': 成功, '2': 失败)
        """
        sql = """
        UPDATE dm.dm_douyin_oceanengine_click_conversion 
        SET callback_status = %s, update_dtm = %s
        WHERE callback_clickid = %s
          and click_timestamp::date = current_date - 1
          and callback_status = '0'
        """
        
        try:
            conn = psycopg2.connect(**self.db_config)
            cursor = conn.cursor()
            
            cursor.execute(sql, (status, datetime.now(), clickid))
            affected_rows = cursor.rowcount
            
            conn.commit()
            
            self.logger.info(f"更新clickid {clickid} 状态为 {status}，影响行数: {affected_rows}")
            
        except Exception as e:
            self.logger.error(f"更新clickid {clickid} 状态失败: {e}")
            if 'conn' in locals():
                conn.rollback()
            raise
        finally:
            if 'cursor' in locals():
                cursor.close()
            if 'conn' in locals():
                conn.close()
    
    def process_conversions(self):
        """
        处理转化上传的主流程
        """
        self.logger.info("开始处理抖音巨量转化上传")
        
        try:
            # 1. 获取待回传的clickid列表
            clickids = self.get_pending_clickids()
            
            if not clickids:
                self.logger.info("没有待回传的clickid，处理完成")
                return
            
            success_count = 0
            failure_count = 0
            
            # 2. 逐个处理clickid
            for clickid in clickids:
                try:
                    # 调用上报接口
                    success, response_data = self.upload_conversion(clickid)
                    
                    if success:
                        # 3. 更新状态为成功
                        self.update_callback_status(clickid, '1')
                        success_count += 1
                    else:
                        # 3. 更新状态为失败
                        self.update_callback_status(clickid, '2')
                        failure_count += 1
                        
                except Exception as e:
                    self.logger.error(f"处理clickid {clickid} 时发生异常: {e}")
                    try:
                        # 发生异常时也标记为失败
                        self.update_callback_status(clickid, '2')
                        failure_count += 1
                    except Exception as update_e:
                        self.logger.error(f"更新clickid {clickid} 失败状态时发生异常: {update_e}")
            
            self.logger.info(f"处理完成 - 成功: {success_count}, 失败: {failure_count}, 总计: {len(clickids)}")
            
        except Exception as e:
            self.logger.error(f"处理转化上传时发生异常: {e}")
            raise

# 使用示例
if __name__ == '__main__':
    # 创建上传器并处理转化（使用默认配置）
    uploader = DouyinConversionUploader()
    uploader.process_conversions()
