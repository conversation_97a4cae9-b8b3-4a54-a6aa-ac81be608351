import requests
import hashlib
import json
import time
import math


BASE_URL = "https://api.tanmarket.cn:20066"  # url地址
APP_ID = "wx261bb2bb281b1cce_oJgIXd"  # app_id
APP_KEY = "tecLlrhz1elWG2PsbpAlGXH0Z6ph6BZt"  # 秘钥


def get_qw_customer_of_sales():
    """获取企微好友客户
    :return:
    """
    page_size = 20

    try:
        data = {"qwUserId": "dashjie", "pageSize": page_size, "pageNo": 1}

        api = "/api/v2/customer/qw-list-customer-of-sales"
        url = BASE_URL + api

        response = post_request(url, data)

        if response['code'] != 0:
            print("****请求失败，返回信息: [{0}]\n".format(response))
            raise Exception

        total_cnt = response["data"]["totalCount"]
        page_cnt = math.ceil(total_cnt / page_size)
        print("销售企微id: {0}, 客户总数: {1}".format(data["qwUserId"], total_cnt))

        if total_cnt == 0:
            return

        result = response["data"]["result"]
        for item in result:
            print("{0},{1},{2}".format(data["qwUserId"], item["externalUserId"], item["wechatNickname"]))

        # 翻页
        for i in range(2, page_cnt + 1):
            data.update({"pageNo": i})
            response = post_request(url, data)
            result = response["data"]["result"]
            for item in result:
                print("{0},{1},{2}".format(data["qwUserId"], item["externalUserId"], item["wechatNickname"]))
    except Exception as e:
        print(e)



def post_request(url, data):
    """ 处理 post 请求
    :param url:
    :param data:
    :param header:
    :return:
    """

    retry_times = 0         # 已重试次数
    max_retry_times = 1     # 最大重试次数

    json_data = json.dumps(data, ensure_ascii=False)
    headers = get_sign_header(json_data)

    print("正在发送请求\n****请求头：{0}\n****请求体：{1}".format(headers, json_data))

    while retry_times <= max_retry_times:

        if retry_times > 0:
            print("****第" + str(retry_times) + "次失败重试")

        try:
            response = requests.post(url, data=json_data.encode("utf-8"), headers=headers)
            # print("{0}:{1}".format(response.status_code, response.text))
            json_response = json.loads(response.text)

            if json_response["code"] == 0:
                break
            else:
                retry_times += 1
                time.sleep(1)

        except Exception as e:
            print(e)
            retry_times += 1
            # time.sleep(1)

    return json_response



def get_sign_header(req_body):
    """ 签名
    :param req_body: 请求体, json格式, utf-8编码
    :return: 带签名的请求头
    """

    timestamp = int(round(time.time() * 1000))
    msg = APP_ID + str(timestamp) + req_body + APP_KEY

    sign = hashlib.sha256(msg.encode('utf-8')).hexdigest()
    # print(sign)

    headers = {
        "appId": APP_ID,
        "timestamp": str(timestamp),
        "sign": sign,
        "Content-Type": "application/json;charset=UTF-8"
    }
    return headers


if __name__ == '__main__':
    get_qw_customer_of_sales()
