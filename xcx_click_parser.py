import json
import re
import psycopg2
from datetime import datetime
from urllib.parse import parse_qs, urlparse

class XcxClickParser:
    def __init__(self, db_config):
        """
        初始化解析器
        :param db_config: 数据库连接配置
        """
        self.db_config = db_config
        
    def parse_log_file(self, file_path):
        """
        解析日志文件并入库
        :param file_path: 日志文件路径
        """
        try:
            conn = psycopg2.connect(**self.db_config)
            cursor = conn.cursor()
            
            # 清空目标表（可选）
            cursor.execute("TRUNCATE TABLE ods.raw_xcx_click")
            
            with open(file_path, 'r', encoding='utf-8') as f:
                for line_no, line in enumerate(f, 1):
                    try:
                        # 解析包含 "data":"XcxClick [解析内容]" 的行
                        click_data = self.extract_xcx_click_data(line.strip())
                        if click_data:
                            if click_data.get('click'):   
                                print(f"解析第{line_no}行: {click_data}")
                                self.insert_to_db(cursor, click_data)
                            
                    except Exception as e:
                        print(f"解析第{line_no}行出错: {e}")
                        continue
            
            conn.commit()
            print("数据解析入库完成")
            
        except Exception as e:
            conn.rollback()
            print(f"处理文件出错: {e}")
            raise
        finally:
            cursor.close()
            conn.close()
    
    def extract_xcx_click_data(self, log_line):
        """
        从日志行中提取XcxClick数据
        :param log_line: 日志行内容
        :return: 解析后的数据字典
        """
        # 匹配 "data":"XcxClick [解析内容]" 模式
        pattern = r'"data"\s*:\s*"XcxClick\s+(.+?)"'
        match = re.search(pattern, log_line)
        
        if not match:
            return None
            
        click_content = match.group(1)
        
        # 解析click内容，假设是JSON格式或键值对格式
        try:
            # 尝试JSON解析
            if click_content.startswith('{'):
                data = json.loads(click_content)
            else:
                # 解析键值对格式 key1=value1&key2=value2
                data = self.parse_key_value_pairs(click_content)
                
            return self.map_to_table_fields(data)
            
        except Exception as e:
            print(f"解析click内容失败: {e}, 内容: {click_content}")
            return None
    
    def parse_key_value_pairs(self, content):
        """
        解析键值对格式的内容
        :param content: 键值对字符串，格式如：[proid=, guid1=, pageid=364490, ...]
        :return: 解析后的字典
        """
        data = {}
        
        # 移除方括号
        if content.startswith('[') and content.endswith(']'):
            content = content[1:-1]
        
        # 按逗号分割键值对
        pairs = content.split(', ')
        
        for pair in pairs:
            if '=' in pair:
                key, value = pair.split('=', 1)
                key = key.strip()
                value = value.strip()
                
                # 处理空值
                if value == '':
                    data[key] = ''
                else:
                    # URL解码（如果需要）
                    if key in ['ext2', 'url'] and '%' in value:
                        try:
                            from urllib.parse import unquote
                            value = unquote(value)
                        except:
                            pass  # 如果解码失败，保持原值
                    
                    data[key] = value
        
        return data
    
    def map_to_table_fields(self, data):
        """
        将解析的数据映射到表字段
        :param data: 解析后的数据字典
        :return: 映射后的数据
        """
        # 获取当前时间作为默认值
        current_time = datetime.now()
        dt = current_time.strftime('%Y-%m-%d')
        ts = str(int(current_time.timestamp() * 1000))
        
        # 映射字段
        mapped_data = {
            'proid': data.get('proid', ''),
            'guid1': data.get('guid1', ''),
            'guid2': data.get('guid2', ''),
            'custno': data.get('custno', ''),
            'consno': data.get('consno', ''),
            'hbno': data.get('hbno', ''),
            'pageid': data.get('pageid', ''),
            'pagelevel': data.get('pagelevel', ''),
            'click': data.get('click', ''),
            'htag': data.get('htag', ''),
            'url': data.get('url', ''),
            'network': data.get('network', ''),
            'ext1': data.get('ext1', ''),
            'ext2': data.get('ext2', ''),
            'ext3': data.get('ext3', ''),
            'fid': data.get('fid', ''),
            'ip': data.get('ip', ''),
            'appversion': data.get('appversion', ''),
            'ts': data.get('ts', ts),
            'dt': data.get('dt', dt),
            'unionid': data.get('unionid', '')
        }
        
        return mapped_data
    
    def insert_to_db(self, cursor, data):
        """
        插入数据到数据库
        :param cursor: 数据库游标
        :param data: 要插入的数据
        """
        sql = """
        INSERT INTO ods.raw_xcx_click (
            proid, guid1, guid2, custno, consno, hbno, pageid, pagelevel,
            click, htag, url, network, ext1, ext2, ext3, fid, ip,
            appversion, ts, dt, unionid
        ) VALUES (
            %(proid)s, %(guid1)s, %(guid2)s, %(custno)s, %(consno)s, %(hbno)s,
            %(pageid)s, %(pagelevel)s, %(click)s, %(htag)s, %(url)s, %(network)s,
            %(ext1)s, %(ext2)s, %(ext3)s, %(fid)s, %(ip)s, %(appversion)s,
            %(ts)s, %(dt)s, %(unionid)s
        )
        """
        
        cursor.execute(sql, data)

# 使用示例
if __name__ == '__main__':
    # 数据库配置
    db_config = {
        'dbname': 'testdb',
        'user': 'oper', 
        'password': 'oper',
        'host': '***************',
        'port': 5432
    }
    
    # 创建解析器并处理文件
    parser = XcxClickParser(db_config)
    parser.parse_log_file('data/howbuy-uac-json.log')