#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
抖音巨量转化上传器测试脚本
"""

import unittest
from unittest.mock import Mock, patch, MagicMock
from douyin_conversion_uploader import DouyinConversionUploader

class TestDouyinConversionUploader(unittest.TestCase):
    
    def setUp(self):
        """测试前准备"""
        self.test_db_config = {
            'dbname': 'test_db',
            'user': 'test_user',
            'password': 'test_pass',
            'host': 'localhost',
            'port': 5432
        }
        self.uploader = DouyinConversionUploader(self.test_db_config)
    
    @patch('douyin_conversion_uploader.psycopg2.connect')
    def test_get_pending_clickids(self, mock_connect):
        """测试获取待回传clickid"""
        # 模拟数据库连接和查询结果
        mock_conn = Mock()
        mock_cursor = Mock()
        mock_connect.return_value = mock_conn
        mock_conn.cursor.return_value = mock_cursor
        mock_cursor.fetchall.return_value = [
            ('clickid_1',),
            ('clickid_2',),
            ('clickid_3',)
        ]
        
        # 执行测试
        clickids = self.uploader.get_pending_clickids()
        
        # 验证结果
        self.assertEqual(len(clickids), 3)
        self.assertIn('clickid_1', clickids)
        self.assertIn('clickid_2', clickids)
        self.assertIn('clickid_3', clickids)
        
        # 验证SQL执行
        mock_cursor.execute.assert_called_once()
        mock_cursor.close.assert_called_once()
        mock_conn.close.assert_called_once()
    
    @patch('douyin_conversion_uploader.requests.post')
    def test_upload_conversion_success(self, mock_post):
        """测试成功上传转化"""
        # 模拟成功响应
        mock_response = Mock()
        mock_response.json.return_value = {"code": 0, "message": "成功"}
        mock_post.return_value = mock_response
        
        # 执行测试
        success, response_data = self.uploader.upload_conversion('test_clickid')
        
        # 验证结果
        self.assertTrue(success)
        self.assertEqual(response_data["code"], 0)
        
        # 验证请求参数
        mock_post.assert_called_once()
        call_args = mock_post.call_args
        self.assertEqual(call_args[1]['timeout'], self.uploader.timeout)
    
    @patch('douyin_conversion_uploader.requests.post')
    def test_upload_conversion_failure(self, mock_post):
        """测试上传转化失败"""
        # 模拟失败响应
        mock_response = Mock()
        mock_response.json.return_value = {"code": 1, "message": "失败"}
        mock_post.return_value = mock_response
        
        # 执行测试
        success, response_data = self.uploader.upload_conversion('test_clickid')
        
        # 验证结果
        self.assertFalse(success)
        
        # 验证重试次数（应该重试1次，总共调用2次）
        self.assertEqual(mock_post.call_count, 2)
    
    @patch('douyin_conversion_uploader.psycopg2.connect')
    def test_update_callback_status(self, mock_connect):
        """测试更新回传状态"""
        # 模拟数据库连接
        mock_conn = Mock()
        mock_cursor = Mock()
        mock_connect.return_value = mock_conn
        mock_conn.cursor.return_value = mock_cursor
        mock_cursor.rowcount = 1
        
        # 执行测试
        self.uploader.update_callback_status('test_clickid', '1')
        
        # 验证SQL执行
        mock_cursor.execute.assert_called_once()
        mock_conn.commit.assert_called_once()
        mock_cursor.close.assert_called_once()
        mock_conn.close.assert_called_once()
    
    @patch.object(DouyinConversionUploader, 'update_callback_status')
    @patch.object(DouyinConversionUploader, 'upload_conversion')
    @patch.object(DouyinConversionUploader, 'get_pending_clickids')
    def test_process_conversions(self, mock_get_clickids, mock_upload, mock_update):
        """测试完整处理流程"""
        # 模拟数据
        mock_get_clickids.return_value = ['clickid_1', 'clickid_2']
        mock_upload.side_effect = [(True, {"code": 0}), (False, None)]
        
        # 执行测试
        self.uploader.process_conversions()
        
        # 验证调用次数
        self.assertEqual(mock_get_clickids.call_count, 1)
        self.assertEqual(mock_upload.call_count, 2)
        self.assertEqual(mock_update.call_count, 2)
        
        # 验证状态更新
        mock_update.assert_any_call('clickid_1', '1')  # 成功
        mock_update.assert_any_call('clickid_2', '2')  # 失败

def run_manual_test():
    """手动测试函数，用于实际环境测试"""
    print("开始手动测试...")
    
    # 创建上传器实例
    uploader = DouyinConversionUploader()
    
    try:
        # 测试获取clickid（注意：这会连接真实数据库）
        print("测试获取待回传clickid...")
        clickids = uploader.get_pending_clickids()
        print(f"获取到 {len(clickids)} 个clickid")
        
        if clickids:
            # 只测试第一个clickid
            test_clickid = clickids[0]
            print(f"测试上传clickid: {test_clickid}")
            
            # 注意：这会调用真实的抖音API
            success, response = uploader.upload_conversion(test_clickid)
            print(f"上传结果: {success}, 响应: {response}")
            
            # 更新状态
            status = '1' if success else '2'
            uploader.update_callback_status(test_clickid, status)
            print(f"状态更新完成: {status}")
        
    except Exception as e:
        print(f"测试过程中发生错误: {e}")

if __name__ == '__main__':
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == 'manual':
        # 手动测试模式
        run_manual_test()
    else:
        # 单元测试模式
        unittest.main()
