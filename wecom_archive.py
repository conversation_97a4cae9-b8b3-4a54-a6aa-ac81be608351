import json
import time
from datetime import datetime
import subprocess
import requests
import psycopg2



class WecomChatArchiveEncryGp:
    def __init__(self, conn_id="postgres_ods", fid_min=1, fid_max=99999999999):
        self.conn_id = conn_id
        self.fid_min = fid_min
        self.fid_max = fid_max
        self._url = 'https://qyapi.weixin.qq.com'
        self._cropid = 'wx261bb2bb281b1cce'

    def find_unhandled_file(self):
        """
        查找未处理的文件，根据文件的更新时间
        :return:
        """
        sql_sel = "select count(1) from ods.raw_wecom_chat_file_import where file_name = %s"
        sql_ins = "insert into ods.raw_wecom_chat_file_import(file_name, status, create_time) values (%s, %s, %s)"

        try:
            # conn = PostgresHook(self.conn_id).get_conn()
            conn = psycopg2.connect(dbname="testdb", user="oper", password="oper", host="*************", port="5432")
            cursor = conn.cursor()

            cwd = "/usr/local/airflow/resources/wecom/chat_history/"
            # 查找30天内&5分钟前修改过的json文件，遍历深度2
            cmd = "find {} -maxdepth {} -mtime -{} -mmin +{} -type f -name '*.json'".format(cwd, 2, 30, 5)
            proc = subprocess.Popen(cmd, shell=True, stdout=subprocess.PIPE, cwd=cwd)
            for line in proc.stdout.readlines():
                file_name = line.decode("utf-8").replace("\r", '').replace("\n", "").strip()
                # print(file_name)
                cursor.execute(sql_sel, (file_name,))
                q_ret = cursor.fetchone()
                if q_ret[0] > 0:
                    continue

                # # 初始化记录状态：0-未导入
                cursor.execute(sql_ins, (file_name, '0', datetime.now()))

            conn.commit()
        except Exception as e:
            conn.rollback()
            raise e
        finally:
            cursor.close()
            conn.close()

    def process_unhandled_file(self):
        """
        导入未处理的文件
        :return:
        """

        sql_sel = "select id, file_name " \
                  "  from ods.raw_wecom_chat_file_import " \
                  " where status = '0' and id >= %s and id <= %s " \
                  " order by id"

        sql_upd = "update ods.raw_wecom_chat_file_import " \
                  "   set status = %s, tot_lines = %s, ins_lines = %s, update_time = %s " \
                  " where id = %s"

        sql_del_sub = "delete from ods.raw_wecom_chat_sub t " \
                      " using ods.raw_wecom_chat s " \
                      " where t.msg_id = s.msg_id " \
                      "   and s.msg_datetime < date_trunc('month', now() - interval '2 month') "

        sql_del = "delete from ods.raw_wecom_chat a " \
                  " where a.msg_datetime < date_trunc('month', now() - interval '2 month') "

        try:
            conn = psycopg2.connect(dbname="testdb", user="oper", password="oper", host="***************", port="5432")
            cursor = conn.cursor()

            # # 非接口加密表, 删除当月、上月, 上上月范围以外的数据, 注意删除顺序： 先删除子表，再删除主表
            # cursor.execute(sql_del_sub)
            # cursor.execute(sql_del)

            cursor.execute(sql_sel, [self.fid_min, self.fid_max])
            file_list = cursor.fetchall()
            # print(file_list)

            for item in file_list:
                # 开始导入，更新记录状态：1-导入中
                cursor.execute(sql_upd, ("1", None, None, datetime.now(), item[0]))
                conn.commit()

                # 导入文件
                print("[{} {}] is importing ...".format(item[0], item[1]))
                tot_lines, ins_lines = self.import_json(conn, item[0], item[1])

                # 导入结束, 更新记录状态：2-导入完成
                cursor.execute(sql_upd, ("2", tot_lines, ins_lines, datetime.now(), item[0]))
                conn.commit()

        except Exception as e:
            # 报错，重置状态 0-未导入
            cursor.execute(sql_upd, ("0", None, None, datetime.now(), item[0]))
            conn.commit()
            raise e
        finally:
            cursor.close()
            conn.close()

    def import_json(self, db_connect, file_id, file_path):
        """
        导入json文件，保存到数据库
        :param db_connect:
        :param file_id:
        :param file_path:
        :return:
        """

        # 使用接口加密数据
        sql = "insert into ods.raw_wecom_chat_history(msg_id,action,send_from,send_tolist,room_id,msg_unixtime," \
              "msg_datetime,msg_type,content,title,seq,file_id,line_no,insert_time) " \
              " values (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)"

        sql_sub = "insert into ods.raw_wecom_chat_history_sub(msg_id,msg_type,msg_unixtime,msg_datetime,content,insert_time) " \
                  " values (%s, %s, %s, %s, %s, %s)"

        # 保留2个月非接口加密数据
        sql_2 = "insert into ods.raw_wecom_chat(msg_id,action,send_from,send_tolist,room_id,msg_unixtime," \
                "msg_datetime,msg_type,content,title,seq,file_id,line_no,insert_time) " \
                " values (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)"

        sql_sub_2 = "insert into ods.raw_wecom_chat_sub(msg_id,msg_type,msg_unixtime,msg_datetime,content,insert_time) " \
                    " values (%s, %s, %s, %s, %s, %s)"

        sql_upt = "update ods.raw_wecom_chat t" \
                  " set content = encode(encrypt(convert_to(t.content, 'UTF8'), convert_to(t.msg_id, 'UTF8'), 'aes'), 'base64') " \
                  " where t.file_id = %s " \
                  " and t.msg_type in ('text')"

        sql_upt_sub = "update ods.raw_wecom_chat_sub t" \
                      " set content = encode(encrypt(convert_to(t.content, 'UTF8'), convert_to(t.msg_id, 'UTF8'), 'aes'), 'base64') " \
                      " from ods.raw_wecom_chat s" \
                      " where t.msg_id = s.msg_id" \
                      " and s.file_id = %s " \
                      " and t.msg_type in ('ChatRecordText', 'text', 'ChatRecordMixed', 'chatrecord')"

        line_no = 0
        tot_lines = 0  # 文件总行数
        ins_lines = 0  # 解析入库的行数（action="send"的入库,其它不入库）
        try:
            cursor = db_connect.cursor()
            with open(file_path, 'r', encoding='utf-8') as json_file:
                line = json_file.readline()
                while line:
                    line_no += 1
                    tot_lines += 1

                    # 加密落库
                    result = self.parse_line(file_id, line_no, line)
                    # print(result)
                    if result and result.get("data"):
                        cursor.execute(sql, result.get("data"))
                        ins_lines += 1

                    '''
                    if result and result.get("msg_type") in ("chatrecord", "mixed") and result.get("sub_data"):
                        cursor.executemany(sql_sub, result.get("sub_data"))
                    '''

                    '''
                    # 非接口加密落库
                    result_2 = self.parse_line_v2(file_id, line_no, line)
                    # print(result_2)
                    if result_2 and result_2.get("data"):
                        cursor.execute(sql_2, result_2.get("data"))

                    if result_2 and result_2.get("msg_type") in ("chatrecord", "mixed") and result_2.get("sub_data"):
                        cursor.executemany(sql_sub_2, result_2.get("sub_data"))
                    '''

                    line = json_file.readline()

            # cursor.execute(sql_upt, [file_id])
            # cursor.execute(sql_upt_sub, [file_id])

            db_connect.commit()

            return tot_lines, ins_lines

        except Exception as e:
            db_connect.rollback()
            print("import raise error: [{0},{1}]--{2}".format(file_id, file_path, line))
            raise e
        finally:
            cursor.close()

    @classmethod
    def parse_line(cls, file_id, line_no, line):
        """
        解析json格式的字符串行
        :param file_id:  文件id， <raw_wecom_chat_file_import>.[id] 取值
        :param line_no:  json文件行号
        :param line: 行内容
        :return:
        """
        data = json.loads(line, strict=False)
        # 消息id, 消息的唯一标识，可根据此字段进行消息去重
        msg_id = data.get("msgid")
        # 消息动作：send（发送消息）； recall（撤回消息）；switch（切换企业日志）
        action = data.get("action")
        # 消息类型：text（文本）；image（图片）；voice（语音）；video（视频）；file（文件）； link（链接）；chatrecord（会话记录）...
        msg_type = data.get("msgtype")
        # 消息发送方id，同一企业内容为user_id, 非相同企业为external_userid; 消息如果是机器人发出，也为 external_userid
        send_from = data.get("from")
        # 消息接收方, 同一个企业内容为userid，非相同企业为external userid （string数组）
        send_tolist = data.get("tolist")
        # 群聊消息的群群id, 如果是单聊则为空
        room_id = data.get("roomid")
        # 消息发送时间戳，utc时间，ms单位
        msg_unixtime = data["msgtime"] if data.get("msgtime") else None
        # msgtime 转为日期时间字符串
        msg_datetime = int_to_datetime(data.get("msgtime"))
        # 消息序列号
        seq = data.get("seq")
        # ETL数据插入时间
        insert_time = datetime.now()

        ret = {"msg_type": msg_type, "data": None, "sub_data": None}  # 解析结果

        if action and action.lower() in ['recall']:
            if msg_type in ("markdown", "qydiskfile"):
                content = data.get("info")
            else:
                content = data.get(msg_type)

            if msg_type == "text":
                text = content.get("content")
                encrypt_result = msg_encrypt([text])  # 加密
                ret["data"] = (msg_id,
                               action,
                               send_from,
                               send_tolist,
                               room_id,
                               msg_unixtime,
                               msg_datetime,
                               msg_type,
                               encrypt_result.get(text),  # 获取加密后字符串
                               None,
                               seq,
                               file_id,
                               line_no,
                               insert_time)
            elif msg_type == "revoke":
                ret["data"] = (msg_id,
                               action,
                               send_from,
                               send_tolist,
                               room_id,
                               msg_unixtime,
                               msg_datetime,
                               msg_type,
                               content.get("pre_msgid"),  # 撤回的消息ID
                               None,
                               seq,
                               file_id,
                               line_no,
                               insert_time)
            elif msg_type in ("chatrecord", "mixed"):
                title = content.get("title")
                ret["data"] = (msg_id,
                               action,
                               send_from,
                               send_tolist,
                               room_id,
                               msg_unixtime,
                               msg_datetime,
                               msg_type,
                               None,    # content 留空，具体内容保存到子表中
                               title,   # title 字段为 "chatrecord" 类型独有
                               seq,
                               file_id,
                               line_no,
                               insert_time)
                ret["sub_data"] = cls.parse_chatrecord_subitem(msg_id, content.get("item"))
            else:
                # 2024-02-01前表情、红包类型不落库
                # 保留原始json字符串（"image", "voice", "video", "file", "link"等类型）
                ret["data"] = (msg_id,
                               action,
                               send_from,
                               send_tolist,
                               room_id,
                               msg_unixtime,
                               msg_datetime,
                               msg_type,
                               json.dumps(content, ensure_ascii=False) if content else None,
                               None,
                               seq,
                               file_id,
                               line_no,
                               insert_time)
        else:
            print('import ignored --- file_id: {0}, line_no: {1}, action: "{2}"'.format(file_id, line_no, action))

        return ret

    @classmethod
    def parse_chatrecord_subitem(cls, msg_id, item_list):
        """
        当消息类型为chatrecord（聊天记录）时，子项中的类型可为：
            "ChatRecordText"    （内容加密存储）
            "ChatRecordLink"    （原值存储）
            "ChatRecordImage"   （原值存储）
            "ChatRecordVideo"   （原值存储）
            "ChatRecordVoice"   （原值存储）
            "ChatRecordFile"    （原值存储）
            "ChatRecordMixed"    (内容项为列表，不再拆分，直接加密存储)
            "chatrecord"         (内容项为列表，不再拆分，直接加密存储)

        :param msg_id: 消息id
        :param item_list: 子项记录列表
        :return:
        """

        ret = []

        for item in item_list:
            # 消息类型
            msg_type = item.get("type")
            # 消息发送时间戳，utc时间，ms单位
            msg_unixtime = item.get("msgtime")
            # msgtime 转为日期时间字符串
            msg_datetime = int_to_datetime(item.get("msgtime"))
            # ETL数据插入时间
            insert_time = datetime.now()

            if msg_type in ("ChatRecordText", "text"):
                content = json.loads(item.get("content"), encoding="utf-8", strict=False)
                text = content.get("content")
                encrypt_result = msg_encrypt([text])  # 加密
                ret.append((msg_id, msg_type, msg_unixtime, msg_datetime, encrypt_result.get(text), insert_time))
            elif msg_type in ("ChatRecordMixed", "chatrecord"):
                text = item.get("content")
                encrypt_result = msg_encrypt([text])  # 加密
                ret.append((msg_id, msg_type, msg_unixtime, msg_datetime, encrypt_result.get(text), insert_time))
            else:
                content = item.get("content")  # 保存原值
                ret.append((msg_id, msg_type, msg_unixtime, msg_datetime, content, insert_time))

        return ret

    @classmethod
    def parse_line_v2(cls, file_id, line_no, line):
        """
        解析json格式的字符串行
        :param file_id:  文件id， <raw_wecom_chat_file_import>.[id] 取值
        :param line_no:  json文件行号
        :param line: 行内容
        :return:
        """
        data = json.loads(line, encoding="utf-8", strict=False)
        # 消息id, 消息的唯一标识，可根据此字段进行消息去重
        msg_id = data.get("msgid")
        # 消息动作：send（发送消息）； recall（撤回消息）；switch（切换企业日志）
        action = data.get("action")
        # 消息类型：text（文本）；image（图片）；voice（语音）；video（视频）；file（文件）； link（链接）；chatrecord（会话记录）...
        msg_type = data.get("msgtype")
        # 消息发送方id，同一企业内容为user_id, 非相同企业为external_userid; 消息如果是机器人发出，也为 external_userid
        send_from = data.get("from")
        # 消息接收方, 同一个企业内容为userid，非相同企业为external userid （string数组）
        send_tolist = data.get("tolist")
        # 群聊消息的群群id, 如果是单聊则为空
        room_id = data.get("roomid")
        # 消息发送时间戳，utc时间，ms单位
        msg_unixtime = data["msgtime"] if data.get("msgtime") else None
        # msgtime 转为日期时间字符串
        msg_datetime = int_to_datetime(data.get("msgtime"))
        # 消息序列号
        seq = data.get("seq")
        # ETL数据插入时间
        insert_time = datetime.now()

        ret = {"msg_type": msg_type, "data": None, "sub_data": None}  # 解析结果

        if action and action.lower() in ['recall']:  # 处理发送和撤回消息
            if msg_type in ("markdown", "qydiskfile"):
                content = data.get("info")
            else:
                content = data.get(msg_type)

            if msg_type == "text":
                text = content.get("content")
                ret["data"] = (msg_id,
                               action,
                               send_from,
                               send_tolist,
                               room_id,
                               msg_unixtime,
                               msg_datetime,
                               msg_type,
                               text,
                               None,
                               seq,
                               file_id,
                               line_no,
                               insert_time)
            elif msg_type == "revoke":
                ret["data"] = (msg_id,
                               action,
                               send_from,
                               send_tolist,
                               room_id,
                               msg_unixtime,
                               msg_datetime,
                               msg_type,
                               content.get("pre_msgid"),  # 撤回的消息ID
                               None,
                               seq,
                               file_id,
                               line_no,
                               insert_time)
            elif msg_type == "qydiskfile":
                ret["data"] = (msg_id,
                               action,
                               send_from,
                               send_tolist,
                               room_id,
                               msg_unixtime,
                               msg_datetime,
                               msg_type,
                               content.get("filename"),  # 企微微盘文件名
                               None,
                               seq,
                               file_id,
                               line_no,
                               insert_time)
            elif msg_type in ("chatrecord", "mixed"):
                title = content.get("title")
                ret["data"] = (msg_id,
                               action,
                               send_from,
                               send_tolist,
                               room_id,
                               msg_unixtime,
                               msg_datetime,
                               msg_type,
                               None,    # content 留空，具体内容保存到子表中
                               title,   # title 字段为 "chatrecord" 类型独有
                               seq,
                               file_id,
                               line_no,
                               insert_time)
                ret["sub_data"] = cls.parse_chatrecord_subitem_v2(msg_id, content.get("item"))
            else:
                # 2024-02-01 前表情、红包类型不落库
                # 保留原始json字符串（"image", "voice", "video", "file", "link"等类型）
                ret["data"] = (msg_id,
                               action,
                               send_from,
                               send_tolist,
                               room_id,
                               msg_unixtime,
                               msg_datetime,
                               msg_type,
                               json.dumps(content, ensure_ascii=False) if content else None,
                               None,
                               seq,
                               file_id,
                               line_no,
                               insert_time)
        else:
            print('import ignored --- file_id: {0}, line_no: {1}, action: "{2}"'.format(file_id, line_no, action))

        return ret

    @classmethod
    def parse_chatrecord_subitem_v2(cls, msg_id, item_list):
        """  不加密
        当消息类型为chatrecord（聊天记录）时，子项中的类型可为：
            "ChatRecordText"    （原值存储）
            "ChatRecordLink"    （原值存储）
            "ChatRecordImage"   （原值存储）
            "ChatRecordVideo"   （原值存储）
            "ChatRecordVoice"   （原值存储）
            "ChatRecordFile"    （原值存储）
            "ChatRecordMixed"    (原值存储)
            "chatrecord"         (原值存储)

        :param msg_id: 消息id
        :param item_list: 子项记录列表
        :return:
        """

        ret = []

        for item in item_list:
            # 消息类型
            msg_type = item.get("type")
            # 消息发送时间戳，utc时间，ms单位
            msg_unixtime = item.get("msgtime")
            # msgtime 转为日期时间字符串
            msg_datetime = int_to_datetime(item.get("msgtime"))
            # ETL数据插入时间
            insert_time = datetime.now()

            if msg_type in ("ChatRecordText", "text"):
                content = json.loads(item.get("content"), encoding="utf-8", strict=False)
                text = content.get("content")
                ret.append((msg_id, msg_type, msg_unixtime, msg_datetime, text, insert_time))
            elif msg_type in ("ChatRecordMixed", "chatrecord"):
                text = item.get("content")
                ret.append((msg_id, msg_type, msg_unixtime, msg_datetime, text, insert_time))
            else:
                content = item.get("content")  # 保存原值
                ret.append((msg_id, msg_type, msg_unixtime, msg_datetime, content, insert_time))

        return ret

    def _get_weixin_token(self, secret):
        """
        获取微信token
        :return:
        """

        api_token = self._url + '/cgi-bin/gettoken?corpid={}&corpsecret={}'.format(self._cropid, secret)

        max_retries = 3
        tries = 0
        success = False

        while tries < max_retries and not success:
            try:
                res = requests.get(url=api_token)
                if str(res.json().get("errcode")) == '0':
                    success = True
                else:
                    raise Exception(res.json())
            except Exception as e:
                print(e)
                tries += 1
                # time.sleep(1)

        if not success:
            raise Exception("获取微信token失败")

        return res.json().get("access_token")

    def get_chat_group_info(self):
        """
        获取群信息，当前仅获取：群名 + 群创建时间
        :return:
        """

        secret_in = 'P7Co-ZU5r6SCsK7hNk1DlS9bdrkQ748WxWlkmhBg2nk'     # 调内部群应用秘钥
        secret_ext = 'q71Rij1lRMOYkr4zGcpnd958ttYF79JDBfcEcJGR57c'    # 调外部群应用秘钥

        token_in = self._get_weixin_token(secret_in)
        token_ext = self._get_weixin_token(secret_ext)

        api_in = self._url + '/cgi-bin/msgaudit/groupchat/get?access_token={}'.format(token_in)
        api_ext = self._url + '/cgi-bin/externalcontact/groupchat/get?access_token={}'.format(token_ext)

        post_data_in = {"roomid": None}
        post_data_ext = {"chat_id": None, "need_name": 0}

        sql_ins = "insert into ods.raw_wecom_chat_room(room_id) " \
                  "select t1.room_id from (select distinct a.room_id " \
                  "  from ods.raw_wecom_chat_history a " \
                  " where a.msg_datetime >= current_date -2 " \
                  "   and coalesce(a.room_id, '') != '') t1 " \
                  " where not exists (select * from ods.raw_wecom_chat_room t2 where t1.room_id = t2.room_id)"

        sql_sel = "select room_id from ods.raw_wecom_chat_room where coalesce(room_id, '') != '' and etl_time is null order by room_id"
        sql_upd = "update ods.raw_wecom_chat_room set room_type = %s, room_name = %s, creator = %s, create_time = %s, etl_time = now() where room_id = %s"
        sql_err = "update ods.raw_wecom_chat_room set err_msg = %s, etl_time = now() where room_id = %s"

        try:
            conn = psycopg2.connect(dbname="testdb", user="oper", password="oper", host="***************", port="5432")
            cursor = conn.cursor()

            # room_id 增量数据落表
            cursor.execute(sql_ins)

            # 处理增量数据, 获取群名
            cursor.execute(sql_sel)
            roomid_list = cursor.fetchall()

            # 报错信息
            err_info = {'msgaudit': None, 'externalcontact': None}

            for r in roomid_list:
                # 首先，按外部群调用
                post_data_ext["chat_id"] = r[0]
                resp = self._post_weixin_request(api_ext, post_data_ext)

                if str(resp["errcode"]) == '0':
                    group_chat = resp.get("group_chat")
                    cursor.execute(sql_upd, ['外部群', group_chat["name"], group_chat["owner"], int_to_datetime(group_chat["create_time"]), r[0]])
                    print(r[0] + ': 调外部群成功')
                else:
                    err_info["externalcontact"] = {"errcode": resp["errcode"], "errmsg": resp["errmsg"]}
                    print(r[0] + ': 调外部群失败, ' + json.dumps(err_info["externalcontact"]))

                    # 按外部群调用失败，则按内部群调用
                    post_data_in["roomid"] = r[0]
                    resp = self._post_weixin_request(api_in, post_data_in)

                    if str(resp["errcode"]) == '0':
                        cursor.execute(sql_upd, ['内部群', resp["roomname"], resp["creator"], int_to_datetime(resp["room_create_time"]), r[0]])
                        print(r[0] + ': 调内部群成功')
                    else:
                        err_info["msgaudit"] = {"errcode": resp["errcode"], "errmsg": resp["errmsg"]}
                        print(r[0] + ': 调内部群失败, ' + json.dumps(err_info["msgaudit"]))

                        # 记录报错信息
                        cursor.execute(sql_err, [json.dumps(err_info), r[0]])

                err_info.update({'msgaudit': None, 'externalcontact': None})

            conn.commit()

        except Exception as e:
            conn.rollback()
            raise e
        finally:
            cursor.close()
            conn.close()

    @classmethod
    def _post_weixin_request(cls, url, data):
        """ 处理 post 请求
        :param url:
        :param data:
        :return:
        """

        max_retries = 3
        tries = 0
        success = False

        json_data = json.dumps(data, ensure_ascii=False)

        while not success and tries <= max_retries:
            if tries > 0:
                print(url + ": 第" + str(tries) + "次失败重试")
            try:
                res = requests.post(url, data=json_data.encode("utf-8"))
                res_json = res.json()
                success = True
            except Exception as e:
                print(e)
                tries += 1
                # time.sleep(1)

        return res_json



def int_to_datetime(val):
    """
    :param val: int类型unix时间戳, 如 1651676644040（精确到毫秒）; 1577811661(精确到秒)
    :return: 返回datetime
    """
    if len(str(val)) == 10:  # 精确到秒
        time_val = time.localtime(val)
        temp = time.strftime("%Y-%m-%d %H:%M:%S", time_val)
        datetime_val = datetime.strptime(temp, "%Y-%m-%d %H:%M:%S")
    elif 10 < len(str(val)) < 15:  # 精确到毫秒
        k = len(str(val)) - 10
        datetime_val = datetime.fromtimestamp(1.0*val/(10**k))
    else:
        datetime_val = None

    return datetime_val

def msg_encrypt(text_list):
    """
    :param text_list: 文本列表
    :return:
    """

    url = 'http://10.11.102.153:8080/encryptBatch'
    headers = {"Content-Type": "application/json"}
    text_msgs = (bytes(json.dumps({'texts': text_list}), 'utf-8'))

    max_retries = 3
    tries = 0
    success = False

    while tries < max_retries and not success:
        try:
            res = requests.post(url=url, data=text_msgs, headers=headers)
            if res.json().get("retCode") == '0000000':
                success = True
            else:
                raise Exception(res.json())
        except Exception as e:
            print(e)
            tries += 1
            time.sleep(1)

    if not success:
        raise Exception("调用接口加密失败")

    return res.json().get("codecMap")


def msg_decrypt(text_list):
    """
    :param text_list: 文本列表
    :return:
    """

    url = 'http://10.11.102.153:8080/decryptBatch'
    headers = {"Content-Type": "application/json"}
    text_msgs = (bytes(json.dumps({'texts': text_list}), 'utf-8'))

    max_retries = 3
    tries = 0
    success = False

    while tries < max_retries and not success:
        try:
            res = requests.post(url=url, data=text_msgs, headers=headers)
            if res.json().get("retCode") == '0000000':
                success = True
            else:
                raise Exception(res.json())
        except Exception as e:
            print(e)
            tries += 1
            time.sleep(1)

    if not success:
        print(text_list)
        raise Exception("decryptBatch failed")

    return res.json().get("codecMap")




if __name__ == '__main__':
    wecom_archive = WecomChatArchiveEncryGp()
    wecom_archive.process_unhandled_file()