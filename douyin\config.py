# 数据库配置
DATABASE_CONFIG = {
    'dbname': 'testdb',
    'user': 'oper',
    'password': 'oper',
    'host': '***************',
    'port': 5432
}

# 抖音巨量API配置
DOUYIN_API_CONFIG = {
    'conversion_url': 'https://analytics.oceanengine.com/api/v2/conversion',
    'event_type': 'work_wechat_added',
    'timeout': 30,
    'max_retries': 1
}

# 日志配置
LOGGING_CONFIG = {
    'level': 'INFO',
    'format': '%(asctime)s - %(levelname)s - %(message)s',
    'filename': 'douyin_conversion.log'  # 可选，如果不设置则输出到控制台
}
