#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
抖音巨量转化上传器运行脚本
支持命令行参数和不同运行模式
"""

import sys
import argparse
import logging
from datetime import datetime
from douyin_conversion_uploader import DouyinConversionUploader

def setup_logging(log_level='INFO', log_file=None):
    """
    设置日志配置
    :param log_level: 日志级别
    :param log_file: 日志文件路径
    """
    log_format = '%(asctime)s - %(levelname)s - %(message)s'
    
    if log_file:
        logging.basicConfig(
            level=getattr(logging, log_level.upper()),
            format=log_format,
            filename=log_file,
            filemode='a'
        )
    else:
        logging.basicConfig(
            level=getattr(logging, log_level.upper()),
            format=log_format
        )

def run_conversion_upload(db_config=None, dry_run=False):
    """
    运行转化上传
    :param db_config: 数据库配置
    :param dry_run: 是否为试运行模式
    """
    logger = logging.getLogger(__name__)
    
    try:
        logger.info("=" * 50)
        logger.info("开始执行抖音巨量转化上传任务")
        logger.info(f"执行时间: {datetime.now()}")
        logger.info(f"试运行模式: {dry_run}")
        logger.info("=" * 50)
        
        # 创建上传器
        uploader = DouyinConversionUploader(db_config)
        
        if dry_run:
            # 试运行模式：只获取数据，不实际上传
            logger.info("试运行模式：只获取待回传数据，不实际上传")
            clickids = uploader.get_pending_clickids()
            logger.info(f"试运行结果：找到 {len(clickids)} 个待回传的clickid")
            
            if clickids:
                logger.info("待回传的clickid列表（前10个）:")
                for i, clickid in enumerate(clickids[:10]):
                    logger.info(f"  {i+1}. {clickid}")
                if len(clickids) > 10:
                    logger.info(f"  ... 还有 {len(clickids) - 10} 个")
        else:
            # 正常模式：执行完整流程
            uploader.process_conversions()
        
        logger.info("=" * 50)
        logger.info("抖音巨量转化上传任务执行完成")
        logger.info("=" * 50)
        
    except Exception as e:
        logger.error(f"执行转化上传任务时发生异常: {e}")
        raise

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='抖音巨量转化上传器')
    
    parser.add_argument(
        '--dry-run', 
        action='store_true',
        help='试运行模式，只查询数据不实际上传'
    )
    
    parser.add_argument(
        '--log-level',
        choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'],
        default='INFO',
        help='日志级别 (默认: INFO)'
    )
    
    parser.add_argument(
        '--log-file',
        help='日志文件路径 (默认: 输出到控制台)'
    )
    
    parser.add_argument(
        '--db-host',
        help='数据库主机地址'
    )
    
    parser.add_argument(
        '--db-port',
        type=int,
        help='数据库端口'
    )
    
    parser.add_argument(
        '--db-name',
        help='数据库名称'
    )
    
    parser.add_argument(
        '--db-user',
        help='数据库用户名'
    )
    
    parser.add_argument(
        '--db-password',
        help='数据库密码'
    )
    
    args = parser.parse_args()
    
    # 设置日志
    setup_logging(args.log_level, args.log_file)
    
    # 构建数据库配置
    db_config = None
    if any([args.db_host, args.db_port, args.db_name, args.db_user, args.db_password]):
        from config import DATABASE_CONFIG
        db_config = DATABASE_CONFIG.copy()
        
        if args.db_host:
            db_config['host'] = args.db_host
        if args.db_port:
            db_config['port'] = args.db_port
        if args.db_name:
            db_config['dbname'] = args.db_name
        if args.db_user:
            db_config['user'] = args.db_user
        if args.db_password:
            db_config['password'] = args.db_password
    
    try:
        # 执行转化上传
        run_conversion_upload(db_config, args.dry_run)
        sys.exit(0)
        
    except KeyboardInterrupt:
        logging.getLogger(__name__).info("用户中断执行")
        sys.exit(1)
        
    except Exception as e:
        logging.getLogger(__name__).error(f"程序执行失败: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()
