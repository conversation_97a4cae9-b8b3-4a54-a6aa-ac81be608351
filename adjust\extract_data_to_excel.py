import json
import pandas as pd

def extract_data_to_excel(json_file_path):
    """
    从request.json中提取数据并保存到Excel文件
    """
    # 读取JSON文件
    with open(json_file_path, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    # 1. 提取客户持仓数据
    cust_position = data.get('cust_position_holding', {})
    
    # 将客户持仓数据转换为DataFrame（转置为行格式）
    cust_df = pd.DataFrame([cust_position])
    
    # 保存客户持仓到Excel
    cust_df.to_excel('客户持仓.xlsx', index=False, sheet_name='客户持仓')
    print("客户持仓数据已保存到: 客户持仓.xlsx")
    
    # 2. 提取推荐产品池数据
    recommend_products = data.get('recommend_product_pool', [])
    
    if recommend_products:
        # 将推荐产品池转换为DataFrame
        products_df = pd.DataFrame(recommend_products)
        
        # 保存推荐产品池到Excel
        products_df.to_excel('推荐产品池.xlsx', index=False, sheet_name='推荐产品池')
        print("推荐产品池数据已保存到: 推荐产品池.xlsx")
        print(f"共提取 {len(recommend_products)} 个产品")
    else:
        print("未找到推荐产品池数据")
    
    # 打印数据概览
    print("\n=== 客户持仓数据概览 ===")
    print(f"客户编号: {cust_position.get('hbone_no', 'N/A')}")
    print(f"持仓现金金额: {cust_position.get('holding_cash_amount', 'N/A')}")
    print(f"持仓现金比例: {cust_position.get('holding_cash_proportion', 'N/A')}")
    print(f"持仓市值: {cust_position.get('holding_market_cap', 'N/A')}")
    
    if recommend_products:
        print(f"\n=== 推荐产品池概览 ===")
        print(f"产品总数: {len(recommend_products)}")
        
        # 统计产品类型
        product_types = {}
        for product in recommend_products:
            ptype = product.get('product_type', 'unknown')
            product_types[ptype] = product_types.get(ptype, 0) + 1
        
        print("产品类型分布:")
        for ptype, count in product_types.items():
            print(f"  {ptype}: {count}个")

if __name__ == '__main__':
    # 提取数据
    extract_data_to_excel('data/request.json')