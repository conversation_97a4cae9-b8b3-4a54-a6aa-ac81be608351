# 1. 小程序click表

CREATE TABLE ods.raw_xcx_click (
	proid varchar(100) NULL,
	guid1 varchar(100) NULL,
	guid2 varchar(100) NULL,
	custno varchar(100) NULL,
	consno varchar(100) NULL,
	hbno varchar(100) NULL,
	pageid varchar(100) NULL,
	pagelevel varchar(100) NULL,
	click varchar(100) NULL,
	htag varchar(100) NULL,
	url text NULL,
	network varchar(100) NULL,
	ext1 text NULL,
	ext2 text NULL,
	ext3 text NULL,
	fid varchar(100) NULL,
	ip varchar(100) NULL,
	appversion varchar(100) NULL,
	ts varchar(15) NULL,
	dt varchar(10) NULL,
	unionid varchar(64) NULL
)
DISTRIBUTED BY (dt, ts);

# 2. 解析数据落表
- 目标表：ods.raw_xcx_click
- 源数据文件：data/howbuy-uac-json.log
- 开发语言：python3
- 解析规则： "data":"XcxClick [解析内容]"， 将[解析内容]中的字段提取出来，写入ods.raw_xcx_click


# 3. 抖音巨量转化结果表

