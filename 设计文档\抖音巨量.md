# 1. 小程序click表

CREATE TABLE ods.raw_xcx_click (
	proid varchar(100) NULL,
	guid1 varchar(100) NULL,
	guid2 varchar(100) NULL,
	custno varchar(100) NULL,
	consno varchar(100) NULL,
	hbno varchar(100) NULL,
	pageid varchar(100) NULL,
	pagelevel varchar(100) NULL,
	click varchar(100) NULL,
	htag varchar(100) NULL,
	url text NULL,
	network varchar(100) NULL,
	ext1 text NULL,
	ext2 text NULL,
	ext3 text NULL,
	fid varchar(100) NULL,
	ip varchar(100) NULL,
	appversion varchar(100) NULL,
	ts varchar(15) NULL,
	dt varchar(10) NULL,
	unionid varchar(64) NULL
)
DISTRIBUTED BY (dt, ts);

# 2. 解析数据落表
- 目标表：ods.raw_xcx_click
- 源数据文件：data/howbuy-uac-json.log
- 开发语言：python3
- 解析规则： "data":"XcxClick [解析内容]"， 将[解析内容]中的字段提取出来，写入ods.raw_xcx_click


# 3. 抖音巨量转化结果表
|列名|数据类型|非空|注释|
|---|---|---|---|---|
|id|serial4|TRUE|主键ID|
|unionid|varchar(100)|TRUE|企微unionid|
|external_user_id|varchar(100)|FALSE|外部客户号|
|hbone_no|varchar(64)|FALSE|一账通号|
|hk_hbone_no|varchar(64)|FALSE|香港一账通号|
|nick_name|varchar(100)|FALSE|微信昵称|
|op_assistant|varchar(100)|FALSE|运营助理|
|add_time|timestamp|FALSE|加好友时间|
|first_add_time|timestamp|FALSE|首次加友时间|
|add_way|varchar(10)|FALSE|加友方式|
|state|varchar(100)|FALSE|加友渠道|
|click_pageid|varchar(100)|FALSE|点击事件页面id|
|click_id|varchar(100)|FALSE|点击元素id|
|click_ts|varchar(15)|FALSE|点击时间（unix时间戳）|
|click_timestamp|timestamp|FALSE|点击时间|
|click_recommed_advisor|varchar(100)|FALSE|点击页推荐导师|
|click_link|text|FALSE|点击图片链接|
|acti_rid|int4|FALSE|活动表记录行ID|
|acti_ctime|timestamp|FALSE|活动表记录创建时间|
|callback_clickid|varchar(64)|FALSE|抖音巨量回传参数clickid|
|callback_clickid|varchar(64)|FALSE|抖音巨量回传参数clickid|
|callback_status|varchar(2)|FALSE|数据回传状态|
|callback_clickid|varchar(64)|FALSE|抖音巨量回传参数clickid|
reate_dtm|timestamp|FALSE|记录创建时间|
|callback_clickid|varchar(64)|FALSE|抖音巨量回传参数clickid|
|update_dtm|timestamp|FALSE|记录更新时间|

# 4. 转化结果上传抖音巨量
- 接口地址: https://analytics.oceanengine.com/api/v2/conversion
- 请求方式: POST
- 请求体实例:
{
    "event_type": "form", 
    "context": {
        "ad": {
            "callback": "EPHk9cX3pv4CGJax4ZENKI7w4MDev_4C",//callback 这里需要填写的就是从启动参数里获取的 clickid
        }
       
    },
    "timestamp": 1604888786102
}





